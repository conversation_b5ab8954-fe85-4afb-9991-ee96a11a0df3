// Test script to verify booking API
const testBookingAPI = async () => {
  const baseURL = 'http://localhost:8080/api';
  
  // Test data
  const testBooking = {
    scheduleId: 8570, // From our populated data
    seatNumbers: '1,2',
    totalSeats: 2,
    totalFare: 4200,
    journeyDate: '2025-06-14',
    passengerDetails: [
      { name: '<PERSON>', age: 30, gender: 'male' },
      { name: '<PERSON>', age: 28, gender: 'female' }
    ]
  };

  try {
    console.log('Testing booking API...');
    console.log('Test data:', testBooking);

    const response = await fetch(`${baseURL}/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In real app, this would include authentication headers
      },
      body: JSON.stringify(testBooking)
    });

    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', result);

    if (response.ok) {
      console.log('✅ Booking API test successful!');
      return result;
    } else {
      console.log('❌ Booking API test failed:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Booking API test error:', error);
    return null;
  }
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testBookingAPI();
}

console.log('Booking API test script loaded. Call testBookingAPI() to run the test.');
