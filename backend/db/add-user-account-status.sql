-- Add account status functionality to users table
USE bus_reservation_system;

-- Add account status columns to users table
ALTER TABLE users 
ADD COLUMN account_status ENUM('Active', 'Blocked', 'Suspended') DEFAULT 'Active' AFTER phone,
ADD COLUMN blocked_reason VARCHAR(255) NULL AFTER account_status,
ADD COLUMN blocked_date TIMESTAMP NULL AFTER blocked_reason,
ADD COLUMN unblocked_date TIMESTAMP NULL AFTER blocked_date;
